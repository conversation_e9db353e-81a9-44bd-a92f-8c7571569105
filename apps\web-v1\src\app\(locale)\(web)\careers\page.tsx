// import ServerSiteHandler from '../../server-site-handler'
import { Metadata } from 'next'
import ClientSlugHandler from '~/src/app/(locale)/client-slug-handler'
import { generateMetadataObject } from '~/src/libs/cms/shared/metadata'
import PageContent from '~/src/libs/cms/shared/page-content'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute } from '~/src/libs/constants/cms'
import { getLocale } from '~/src/libs/data/cookies'
import { PageBreadcrumb } from '~/src/modules/layout'

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.pageCareer,
    params: {
      filters: {
        locale,
      },
      populate: 'seo.metaImage',
    },
    spreadData: true,
  })

  const seo = pageData?.seo
  const metadata = generateMetadataObject(seo, pageData?.title)
  return metadata
}

export default async function CareersPage() {
  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.pageCareer,
    params: {
      filters: { locale },
    },
    spreadData: true,
  })

  const localizedSlugs = pageData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = ''
      return acc
    },
    { [locale]: '' },
  )

  return (
    <>
      <ClientSlugHandler localizedSlugs={localizedSlugs} />
      {/* <ServerSiteHandler /> */}
      {/* <CareerDetailsView /> */}
      <PageBreadcrumb items={[{ title: 'Careers' }]} />
      <PageContent pageData={pageData} />
    </>
  )
}
