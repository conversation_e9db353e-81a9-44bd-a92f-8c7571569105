import { Metadata } from 'next'
import ClientSlugHandler from '~/src/app/(locale)/client-slug-handler'
import { generateMetadataObject } from '~/src/libs/cms/shared/metadata'
import PageContent from '~/src/libs/cms/shared/page-content'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute } from '~/src/libs/constants/cms'
import { getLocale } from '~/src/libs/data/cookies'
import { PageBreadcrumb } from '~/src/modules/layout'
import ProductCategoryDetailView from '~/src/modules/product/product-category-detail-view'

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.pageProductCategorySingle,
    params: {
      filters: {
        locale,
      },
      populate: 'seo.metaImage',
    },
    spreadData: true,
  })

  const seo = pageData?.seo
  const metadata = generateMetadataObject(seo)
  return metadata
}

type Props = {
  params: Promise<{ slug: string }>
}

export default async function ProductCategoryDetailPage({ params }: Props) {
  const locale = await getLocale()
  const { slug } = await params

  const pageData = await fetchContentType({
    contentType: apiRoute.pageProductCategorySingle,
    params: {
      filters: { locale },
      // populate: ['seo'],
    },
    spreadData: true,
  })

  let categoryDetailData: any = null
  try {
    categoryDetailData = await fetchContentType({
      contentType: apiRoute.productCategories,
      params: {
        filters: {
          slug,
          locale,
        },
      },
      spreadData: true,
    })
  } catch (error) {
    console.log(error)
  }

  const localizedSlugs = pageData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = ''
      return acc
    },
    { [locale]: '' },
  )

  return (
    <>
      <ClientSlugHandler localizedSlugs={localizedSlugs} />
      <PageBreadcrumb
        items={[
          { title: 'Product Categories', href: '/product-categories' },
          { title: categoryDetailData?.name || '' },
        ]}
      />
      <ProductCategoryDetailView categoryData={categoryDetailData} />
      <PageContent pageData={pageData} />
    </>
  )
}
