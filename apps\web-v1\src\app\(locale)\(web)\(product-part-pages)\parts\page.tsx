import { Metadata } from 'next'
import ClientSlugHandler from '~/src/app/(locale)/client-slug-handler'
import { generateMetadataObject } from '~/src/libs/cms/shared/metadata'
import PageContent from '~/src/libs/cms/shared/page-content'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute } from '~/src/libs/constants/cms'
import { getLocale } from '~/src/libs/data/cookies'
import { PageBreadcrumb } from '~/src/modules/layout'

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.pagePart,
    params: {
      filters: {
        locale: locale,
      },
      populate: 'seo.metaImage',
    },
    spreadData: true,
  })

  const seo = pageData?.seo
  const metadata = generateMetadataObject(seo)
  return metadata
}

export default async function ProductPartsPage() {
  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.pagePart,
    params: {
      filters: { locale: locale },
    },
    spreadData: true,
  })

  const localizedSlugs = pageData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = localization.slug
      return acc
    },
    { [locale]: pageData?.slug },
  )

  return (
    <>
      <ClientSlugHandler localizedSlugs={localizedSlugs} />
      <PageBreadcrumb items={[{ title: 'Parts' }]} />
      <PageContent pageData={pageData} />
    </>
  )
}
